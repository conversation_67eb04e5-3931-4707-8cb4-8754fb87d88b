"""
健康知识 API 接口测试示例

这个文件展示了如何使用健康知识管理接口的示例代码
"""

# API 接口说明

# 1. 健康知识列表（管理端）
# GET /wx_mom/manage/health/knowledge/list/
# 权限：员工 + HEALTH_EDUCATION_VIEW 权限
# 支持分页和搜索（title, summary 字段）
# 返回格式：
"""
{
    "code": 0,
    "msg": "健康知识列表获取成功",
    "data": {
        "list": [
            {
                "rid": "uuid-string$private",
                "title": "产后护理知识",
                "summary": "产后护理的基本知识和注意事项",
                "created_at": "2024-01-01 10:00:00",
                "is_public": false
            }
        ],
        "page": 1,
        "page_size": 10,
        "total_count": 1,
        "total_page": 1
    }
}
"""

# 2. 健康知识详情（管理端）
# GET /wx_mom/manage/health/knowledge/detail/{rid}/
# 权限：员工 + HEALTH_EDUCATION_VIEW 权限
# 返回格式：
"""
{
    "code": 0,
    "msg": "获取健康知识详情成功",
    "data": {
        "rid": "uuid-string$private",
        "title": "产后护理知识",
        "summary": "产后护理的基本知识和注意事项",
        "content": "详细的产后护理内容...",
        "created_at": "2024-01-01 10:00:00",
        "is_public": false
    }
}
"""

# 3. 健康知识创建
# POST /wx_mom/manage/health/knowledge/create/
# 权限：员工 + HEALTH_EDUCATION_EDIT 权限
# 请求体：
"""
{
    "title": "新的健康知识标题",
    "summary": "健康知识摘要",
    "content": "详细的健康知识内容"
}
"""
# 返回格式：
"""
{
    "code": 0,
    "msg": "健康知识创建成功",
    "data": {
        "rid": "new-uuid-string$private",
        "title": "新的健康知识标题",
        "summary": "健康知识摘要",
        "content": "详细的健康知识内容",
        "created_at": "2024-01-01 10:00:00",
        "is_public": false
    }
}
"""

# 4. 健康知识更新
# PUT /wx_mom/manage/health/knowledge/update/{rid}/
# 权限：员工 + HEALTH_EDUCATION_EDIT 权限
# 请求体：
"""
{
    "title": "更新后的标题",
    "summary": "更新后的摘要",
    "content": "更新后的内容"
}
"""
# 返回格式：
"""
{
    "code": 0,
    "msg": "健康知识更新成功",
    "data": {
        "rid": "uuid-string$private",
        "title": "更新后的标题",
        "summary": "更新后的摘要",
        "content": "更新后的内容",
        "created_at": "2024-01-01 10:00:00",
        "is_public": false
    }
}
"""

# 5. 健康知识删除
# DELETE /wx_mom/manage/health/knowledge/delete/{rid}/
# 权限：员工 + HEALTH_EDUCATION_EDIT 权限
# 返回格式：
"""
{
    "code": 0,
    "msg": "健康知识删除成功"
}
"""

# 错误响应示例：
"""
{
    "code": -1,
    "msg": "健康知识不存在"
}
"""

# 数据校验错误示例：
"""
{
    "code": -1,
    "msg": "数据校验失败",
    "data": {
        "title": ["标题不能为空"],
        "summary": ["摘要不能为空"]
    }
}
"""

# 权限错误示例：
"""
{
    "code": -1,
    "msg": "您没有权限执行此操作"
}
"""

# 使用示例（JavaScript/前端）：
"""
// 1. 获取健康知识列表
const getHealthKnowledgeList = async (page = 1, pageSize = 10, search = '') => {
    const response = await fetch('/wx_mom/manage/health/knowledge/list/', {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    });
    return await response.json();
};

// 2. 创建健康知识
const createHealthKnowledge = async (data) => {
    const response = await fetch('/wx_mom/manage/health/knowledge/create/', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    });
    return await response.json();
};

// 3. 更新健康知识
const updateHealthKnowledge = async (rid, data) => {
    const response = await fetch(`/wx_mom/manage/health/knowledge/update/${rid}/`, {
        method: 'PUT',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    });
    return await response.json();
};

// 4. 删除健康知识
const deleteHealthKnowledge = async (rid) => {
    const response = await fetch(`/wx_mom/manage/health/knowledge/delete/${rid}/`, {
        method: 'DELETE',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    });
    return await response.json();
};
"""

# 注意事项：
# 1. 所有管理端接口都需要员工身份认证
# 2. 创建和编辑操作需要 HEALTH_EDUCATION_EDIT 权限
# 3. 查看操作需要 HEALTH_EDUCATION_VIEW 权限
# 4. 所有操作都会记录审计日志
# 5. 数据会自动关联到当前员工的月子中心
# 6. 支持分页和搜索功能
# 7. 所有字段都有基本的数据校验
